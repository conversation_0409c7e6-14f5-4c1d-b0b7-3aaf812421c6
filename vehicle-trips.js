import http from 'k6/http';
import { check } from 'k6';

// Test configuration - Single request test with 10 second timeout
export const options = {
  vus: 1, // 1 virtual user
  duration: '2s', // Run for 10 seconds max
  thresholds: {
    http_req_duration: ['p(95)<10000'], // 95% of requests should be below 10 seconds
    http_req_failed: ['rate<0.01'],     // Less than 1% of requests should fail
  },
};

// Old load test configuration (commented out)
// export const options = {
//   stages: [
//     { duration: '30s', target: 20 }, // Ramp up to 20 users over 30 seconds
//     { duration: '1m', target: 20 },  // Stay at 20 users for 1 minute
//     { duration: '30s', target: 0 },  // Ramp down to 0 users
//   ],
//   thresholds: {
//     http_req_duration: ['p(95)<500'], // 95% of requests should be below 500ms
//     http_req_failed: ['rate<0.01'],   // Less than 1% of requests should fail
//   },
// };

// You can set your bearer token here or use environment variable
const BEARER_TOKEN = __ENV.BEARER_TOKEN || 'eyJ0eXAiOiJKV1QiLCJraWQiOiJqL1VHeWRyVWdZclRDREhwdWRKKzBlcWlYNFk9IiwiYWxnIjoiUFMyNTYifQ.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.ot7qeB339_lH9biCnwqe1Lp3-CjOeCQPEdW6BYAyxT5cmyOQ-0NVuvdQ_3GEcF0hgWtDLxBhbQNZGi48Glt7l7KvUZHYUr3F74pHR1sSW0Gpa8-ZCx5aW35QD6OAr5_D8ilDHHYs0EG7piPGvl6KrciCyg88SszYqDsRKtwYciw2mk2-JoFk8ByGygxNEB5aOd4IBNCfQCxYMy4hFReTFNelRFLN1PWAM3pboZcVAH64TDZczBjwGDqqN73jHV3MWQvASk4yFvXDYJPD7y1vaHTd2FZ0ws5yvsG8GQgBuW60BshW7RgJle15LSM1HEvqEorQoOlQuOO7_kGBXEecQg'

// Test scenario
export default function () {
  const url = "https://location-services-apis.cn-preprod.jlr-vcdp.dcclouds.com/api/v1/vehicles/f5e1cafa-e5e0-47af-b0bd-7bb866762cb0/export/time";
  const payload = JSON.stringify({
    "language": "en",
    "fromDate": "2023-10-24T10:30:22.123",
    "toDate": "2025-10-24T10:30:22.123"
  });
  
  const params = {
    headers: {
      'Authorization': `Bearer ${BEARER_TOKEN}`,
      'Content-Type': 'application/json',
    },
  };

  const response = http.post(url, payload, params);

  // Verify the response
  check(response, {
    'is status 200': (r) => r.status === 200,
    'response time < 10s': (r) => r.timings.duration < 10000,
  });

  // No sleep needed for single request test
  // sleep(Math.random() * 4 + 1);
} 
